import os
import json
import asyncio
import argparse
import traceback
from typing import Dict, List, Optional, Any, Union

from fastapi import Fast<PERSON>I, HTTPException, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse, JSONResponse
from fastapi.exceptions import RequestValidationError
from pydantic import BaseModel, Field, validator

import fastapi_poe as fp
from fastapi_poe.types import ProtocolMessage

from dotenv import load_dotenv

load_dotenv() 

# Content Item的模型定义 - 支持OpenAI新格式
class ContentItem(BaseModel):
    type: str
    text: Optional[str] = None
    image_url: Optional[Dict[str, str]] = None

# OpenAI兼容的请求模型 - 添加更多参数支持 - 支持新的content格式
class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[Dict[str, Any]]  # 将content的类型改为Any，以适应字符串或列表
    stream: bool = False
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None
    top_p: Optional[float] = None
    frequency_penalty: Optional[float] = None
    presence_penalty: Optional[float] = None
    stop: Optional[Union[str, List[str]]] = None
    n: Optional[int] = 1
    user: Optional[str] = None
    
    # 验证消息格式
    @validator('messages')
    def validate_messages(cls, v):
        if not v:
            raise ValueError('messages cannot be empty')
        
        for msg in v:
            if 'role' not in msg:
                raise ValueError('each message must have a role')
            if 'content' not in msg:
                raise ValueError('each message must have content')
            if msg['role'] not in ['system', 'user', 'assistant']:
                raise ValueError(f"role must be one of ['system', 'user', 'assistant'], got {msg['role']}")
            
            # 不再检查content是否为字符串，因为它可能是列表
        
        return v
    
# OpenAI兼容的响应模型
class ChatCompletionResponse(BaseModel):
    id: str = Field(default_factory=lambda: "chatcmpl-" + os.urandom(12).hex())
    object: str = "chat.completion"
    created: int = Field(default_factory=lambda: int(asyncio.get_event_loop().time()))
    model: str
    choices: List[Dict[str, Any]]
    usage: Dict[str, int] = {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}

# 创建FastAPI应用
app = FastAPI(title="Poe OpenAI Compatible API")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 自定义异常处理
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    details = {}
    for error in exc.errors():
        loc = ".".join(str(x) for x in error["loc"])
        details[loc] = error["msg"]
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={"error": {
            "message": "Validation error",
            "details": details,
            "body": await request.json()
        }}
    )

# 存储模型名称到Poe bot名称的映射
MODEL_TO_BOT = {
    "gpt-3.5-turbo": "GPT-3.5-Turbo",  # 使用Poe上的正确bot名称
    "gpt-4": "GPT-4", 
    "gpt-4.5": "GPT-4.5-Preview", 
    "gpt-4.1": "GPT-4.1",
    "gpt-4o": "GPT-4o",
    "claude-3-opus": "Claude-3-Opus",
    "claude-3-sonnet": "Claude-3-Sonnet", 
    "claude-3.5-sonnet": "Claude-3.5-Sonnet",  # 添加Claude-3.5-Sonnet
    "claude-3.7-sonnet": "Claude-3.7-Sonnet",  # 添加Claude-3.7-Sonnet
    "claude-sonnet-4": "claude-sonnet-4",  # 添加Claude-3.7-Sonnet
    "gemini-2.0-pro": "Gemini-Pro",
    "MyClaude1337": "MyClaude1337",
    "gemini-2.5-pro": "Gemini-2.5-Pro",
    "grok-4": "Grok-4",
    # 可以添加更多映射...
}

def get_poe_bot_name(model: str) -> str:
    """根据模型名称获取相应的Poe bot名称"""
    return MODEL_TO_BOT.get(model, model)  # 如果找不到映射，直接使用输入的模型名称

def extract_text_from_content(content: Any) -> str:
    """
    从OpenAI格式的content中提取文本
    支持字符串形式的content和列表形式的content
    """
    if isinstance(content, str):
        return content
    elif isinstance(content, list):
        # 从列表中提取所有文本内容并拼接
        text_parts = []
        for item in content:
            if isinstance(item, dict):
                if item.get("type") == "text" and "text" in item:
                    text_parts.append(item["text"])
        return "".join(text_parts)
    return ""  # 如果无法解析，返回空字符串

def convert_openai_messages_to_poe(messages: List[Dict[str, Any]]) -> List[ProtocolMessage]:
    """将OpenAI格式的消息转换为Poe格式的消息"""
    poe_messages = []
    
    for msg in messages:
        role = msg["role"]
        content = msg["content"]
        
        # 从content中提取文本
        content_text = extract_text_from_content(content)
        
        # 根据fastapi_poe的实际API结构创建ProtocolMessage
        # 注意：根据错误信息，Poe API中的role应为'system', 'user'或'bot'
        if role == "system":
            poe_messages.append(ProtocolMessage(role="system", content=content_text))
        elif role == "user":
            poe_messages.append(ProtocolMessage(role="user", content=content_text))
        elif role == "assistant":
            # 在Poe API中，assistant角色应为'bot'
            poe_messages.append(ProtocolMessage(role="bot", content=content_text))
            
    return poe_messages

@app.post("/v1/chat/completions")
async def chat_completions(request: Request):
    """兼容OpenAI chat completions API的端点"""
    try:
        # 手动解析请求体，为调试提供更多信息
        request_data = await request.json()
        print(f"原始请求数据: {json.dumps(request_data, ensure_ascii=False)[:200]}...")  # 只打印前200个字符避免日志过长
        
        # 手动处理消息内容，不使用Pydantic验证
        if "messages" not in request_data:
            raise HTTPException(status_code=422, detail="Missing required field: messages")
        if "model" not in request_data:
            raise HTTPException(status_code=422, detail="Missing required field: model")
        
        model = request_data["model"]
        messages = request_data["messages"]
        stream = request_data.get("stream", False)
        
        # 验证消息格式
        for i, msg in enumerate(messages):
            if "role" not in msg:
                raise HTTPException(status_code=422, detail=f"Message at index {i} missing required field: role")
            if "content" not in msg:
                raise HTTPException(status_code=422, detail=f"Message at index {i} missing required field: content")
            if msg["role"] not in ["system", "user", "assistant"]:
                raise HTTPException(status_code=422, detail=f"Message at index {i} has invalid role: {msg['role']}")
            
        poe_bot_name = get_poe_bot_name(model)
        poe_messages = convert_openai_messages_to_poe(messages)
        
        # 调试信息
        print(f"Bot name: {poe_bot_name}")
        print(f"转换后的Poe消息: {poe_messages}")
        
        # 获取Poe API密钥，可以从环境变量或者请求头中获取
        poe_api_key = os.environ.get("POE_API_KEY") or request.headers.get("X-Poe-Api-Key")
        if not poe_api_key:
            raise HTTPException(status_code=401, detail="Missing Poe API Key")
        
        # 打印部分API密钥以便调试(安全起见只显示前几个字符)
        print(f"Using API key: {poe_api_key[:4]}...")
        
        # 选择流式响应或非流式响应
        if stream:
            async def generate_stream():
                try:
                    full_response_text = ""
                    
                    # 打印调试信息
                    print("开始流式请求...")
                    
                    # 尝试使用get_bot_response
                    async for partial_response in fp.get_bot_response(
                        messages=poe_messages,
                        bot_name=poe_bot_name,
                        api_key=poe_api_key
                    ):
                        # 打印部分响应信息(只打印类型和属性，避免日志过长)
                        print(f"类型: {type(partial_response)}")
                        print(f"属性: {dir(partial_response)}")
                        
                        # 尝试多种方式获取文本
                        text_delta = ""

                        # 根据实际返回的结构尝试提取文本
                        # 优先使用text_delta（增量文本），如果不存在再使用text
                        if hasattr(partial_response, "text_delta") and partial_response.text_delta:
                            text_delta = partial_response.text_delta
                        elif hasattr(partial_response, "text") and partial_response.text:
                            # 对于某些模型，text可能包含完整文本，需要计算增量
                            current_text = partial_response.text
                            if current_text.startswith(full_response_text):
                                # 如果当前文本以已有文本开头，提取增量部分
                                text_delta = current_text[len(full_response_text):]
                            else:
                                # 否则使用完整文本（可能是第一个响应）
                                text_delta = current_text
                        
                        # 如果成功提取到文本
                        if text_delta:
                            full_response_text += text_delta
                            
                            # 构建与OpenAI兼容的流式响应格式
                            chunk = {
                                "id": f"chatcmpl-{os.urandom(6).hex()}",
                                "object": "chat.completion.chunk",
                                "created": int(asyncio.get_event_loop().time()),
                                "model": model,
                                "choices": [
                                    {
                                        "index": 0,
                                        "delta": {"content": text_delta},
                                        "finish_reason": None
                                    }
                                ]
                            }
                            yield f"data: {json.dumps(chunk)}\n\n"
                    
                    # 发送完成信号
                    done_chunk = {
                        "id": f"chatcmpl-{os.urandom(6).hex()}",
                        "object": "chat.completion.chunk",
                        "created": int(asyncio.get_event_loop().time()),
                        "model": model,
                        "choices": [
                            {
                                "index": 0,
                                "delta": {},
                                "finish_reason": "stop"
                            }
                        ]
                    }
                    yield f"data: {json.dumps(done_chunk)}\n\n"
                    yield "data: [DONE]\n\n"
                    
                    print(f"流式响应完成，总共生成文本长度: {len(full_response_text)}")
                    
                except Exception as e:
                    print(f"流式响应错误: {str(e)}")
                    traceback.print_exc()
                    # 确保错误消息不包含非法字符
                    error_msg = str(e).replace("\n", " ").replace("\r", "")
                    error_chunk = {"error": {"message": error_msg, "type": "server_error"}}
                    yield f"data: {json.dumps(error_chunk)}\n\n"

            return StreamingResponse(
                generate_stream(),
                media_type="text/event-stream"
            )
        else:
            # 收集完整响应
            print("开始非流式请求...")
            response_text = ""
            
            # 使用get_bot_response函数，尝试收集完整响应
            async for partial_response in fp.get_bot_response(
                messages=poe_messages,
                bot_name=poe_bot_name,
                api_key=poe_api_key
            ):
                # 打印部分响应信息(只打印类型和属性，避免日志过长)
                print(f"类型: {type(partial_response)}")
                print(f"属性: {dir(partial_response)}")
                
                # 尝试多种方式获取文本
                # 优先使用text_delta（增量文本），如果不存在再使用text
                if hasattr(partial_response, "text_delta") and partial_response.text_delta:
                    response_text += partial_response.text_delta
                elif hasattr(partial_response, "text") and partial_response.text:
                    # 对于某些模型，text可能包含完整文本，需要避免重复累积
                    current_text = partial_response.text
                    if current_text != response_text and not response_text.endswith(current_text):
                        # 如果当前文本与已有文本不同且不重复，则使用它
                        if current_text.startswith(response_text):
                            # 如果当前文本以已有文本开头，提取增量部分
                            response_text += current_text[len(response_text):]
                        else:
                            # 否则直接使用当前文本（可能是完整响应）
                            response_text = current_text
            
            # 如果没有获取到任何内容，记录警告并返回空响应
            if not response_text:
                print("警告：未能从部分响应中提取出文本内容")
                response_text = "无法获取响应"
            
            print(f"非流式响应完成，总共生成文本长度: {len(response_text)}")
            
            # 构建与OpenAI兼容的响应格式
            response = {
                "id": f"chatcmpl-{os.urandom(12).hex()}",
                "object": "chat.completion",
                "created": int(asyncio.get_event_loop().time()),
                "model": model,
                "choices": [
                    {
                        "index": 0,
                        "message": {"role": "assistant", "content": response_text},
                        "finish_reason": "stop"
                    }
                ],
                "usage": {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}
            }
            
            return response
            
    except Exception as e:
        # 打印完整的异常信息
        print(f"请求处理错误: {str(e)}")
        traceback.print_exc()
        # 确保错误消息不包含非法字符
        error_msg = str(e).replace("\n", " ").replace("\r", "")
        raise HTTPException(status_code=500, detail=f"Error: {error_msg}")

@app.get("/v1/models")
async def list_models():
    """兼容OpenAI models API的端点"""
    models = [
        {"id": "gpt-3.5-turbo", "object": "model", "created": 1677610602, "owned_by": "openai"},
        {"id": "gpt-4", "object": "model", "created": 1687882411, "owned_by": "openai"},
        {"id": "gpt-4.5-preview", "object": "model", "created": 1687882411, "owned_by": "openai"},
        {"id": "gpt-4.1", "object": "model", "created": 1687882411, "owned_by": "openai"},
        {"id": "gpt-4o", "object": "model", "created": 1698950469, "owned_by": "openai"},
        {"id": "claude-3-opus", "object": "model", "created": 1709740467, "owned_by": "anthropic"},
        {"id": "claude-3-sonnet", "object": "model", "created": 1709740467, "owned_by": "anthropic"},
        {"id": "claude-3.5-sonnet", "object": "model", "created": 1708546048, "owned_by": "anthropic"},
        {"id": "claude-3.7-sonnet", "object": "model", "created": 1708546048, "owned_by": "anthropic"},
        {"id": "claude-sonnet-4", "object": "model", "created": 1708546048, "owned_by": "anthropic"},
        {"id": "gemini-pro-2.5", "object": "model", "created": 1702559996, "owned_by": "google"},
        {"id": "gemini-pro", "object": "model", "created": 1702559996, "owned_by": "google"},
        {"id": "grok-4", "object": "model", "created": 1702559996, "owned_by": "google"},
    ]
    return {"object": "list", "data": models}

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run the Poe OpenAI Compatible API server")
    parser.add_argument("--host", type=str, default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=3700, help="Port to bind to")  # 端口改为3700
    
    args = parser.parse_args()
    
    print(f"启动服务器: {args.host}:{args.port}")
    
    import uvicorn
    uvicorn.run(app, host=args.host, port=args.port)
